import styles from './index.module.scss';
import common from './common.module.scss';
import HeroSection from './components/HeroSection';
import AboutUs from './components/AboutUs';
import PyramidAnimation from './components/PyramidAnimation';
import CarouselComponent from './components/CarouselComponent';
import BigQuestions from './components/BigQuestions';
import { useScrollAnimation, useSequentialAnimation } from './hooks/useScrollAnimation';
import { motion } from 'framer-motion';

function App() {
  const [cardsRef, isCardsVisible] = useScrollAnimation(0.1);
  const animatedCards = useSequentialAnimation(isCardsVisible, 3, 200);

  // 平滑滚动到指定部分
  const scrollToSection = (sectionId) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    }
  };

  // Great Weather轮播图片数组
  const greatWeatherCarouselImages = [
    '/images/great_weather/carousel_1.png',
    '/images/great_weather/carousel_2.png',
    '/images/great_weather/carousel_3.png',
    '/images/great_weather/carousel_4.png',
    '/images/great_weather/carousel_5.png',
    '/images/great_weather/carousel_6.png'
  ];

  // PDF Quick Viewer轮播图片数组
  const pdfQuickViewerCarouselImages = [
    '/images/pdf_quick_viewer/carousel_1.png',
    '/images/pdf_quick_viewer/carousel_2.png',
    '/images/pdf_quick_viewer/carousel_3.png',
    '/images/pdf_quick_viewer/carousel_4.png',
    '/images/pdf_quick_viewer/carousel_5.png',
    '/images/pdf_quick_viewer/carousel_6.png'
  ];

  return (
    <div className={common.contont}>
      <div className={styles.a5660BaappsCom}>
        <div className={styles.top} id="home">
          <div className={styles.frame2147229728}>
            <div className={styles.frame}>
              <div
                className={styles.buttonTab5660}
                onClick={() => scrollToSection('home')}
              >
                <p className={styles.home}>Home</p>
              </div>
              <div
                className={styles.buttonTab56602}
                onClick={() => scrollToSection('about')}
              >
                <p className={styles.home}>About</p>
              </div>
              <div
                className={styles.buttonTab5661}
                onClick={() => scrollToSection('apps')}
              >
                <p className={styles.home2}>Apps</p>
              </div>
              <div
                className={styles.buttonTab5663}
                onClick={() => scrollToSection('faqs')}
              >
                <p className={styles.home3}>Faqs</p>
              </div>
              <div
                className={styles.buttonTab5662}
                onClick={() => scrollToSection('joinus')}
              >
                <p className={styles.home4}>Join us</p>
              </div>
            </div>
          </div>
          <HeroSection />
        </div>
        <div id="about">
          <AboutUs />
        </div>
        <div className={styles.boxSection}>
          <img
            src="/images/box.svg"
            alt="Box section with company achievements and trust indicators"
            className={styles.boxSvg}
          />
        </div>
        <div className={styles.weCan} ref={cardsRef}>
          <div className={styles.autoWrapper4}>
            <p className={styles.weCanBeYour}>we can be your</p>
            <p className={styles.tOpChoice}>TOP Choice</p>
          </div>
          <PyramidAnimation
            className={styles.frame2}
          />
          <motion.div
            className={styles.boundlessCard}
            initial={{ opacity: 0, y: 50, scale: 0.8 }}
            animate={animatedCards.has(0) ? { opacity: 1, y: 0, scale: 1 } : { opacity: 0, y: 50, scale: 0.8 }}
            transition={{
              type: "spring",
              damping: 20,
              stiffness: 300,
              mass: 0.8
            }}
          >
            <img
              src="/images/boundless.svg"
              alt="Boundless Synergy, Unified Vision - Cross-disciplinary collaboration card"
              className={styles.boundlessSvg}
            />
          </motion.div>
          <motion.div
            className={styles.precisionCard}
            initial={{ opacity: 0, y: 50, scale: 0.8 }}
            animate={animatedCards.has(1) ? { opacity: 1, y: 0, scale: 1 } : { opacity: 0, y: 50, scale: 0.8 }}
            transition={{
              type: "spring",
              damping: 20,
              stiffness: 300,
              mass: 0.8
            }}
          >
            <img
              src="/images/precision.svg"
              alt="Precision Engineered, Agile Execution - Lean delivery pipelines card"
              className={styles.precisionSvg}
            />
          </motion.div>
          <motion.div
            className={styles.contextualCard}
            initial={{ opacity: 0, y: 50, scale: 0.8 }}
            animate={animatedCards.has(2) ? { opacity: 1, y: 0, scale: 1 } : { opacity: 0, y: 50, scale: 0.8 }}
            transition={{
              type: "spring",
              damping: 20,
              stiffness: 300,
              mass: 0.8
            }}
          >
            <img
              src="/images/contextual.svg"
              alt="Contextual Intelligence, Targeted Resonance - Behavior modeling card"
              className={styles.contextualSvg}
            />
          </motion.div>
        </div>
        <p className={styles.viewOurWork} id="apps">view our work</p>
        <div className={styles.frame2147229701}>
          <div className={styles.rectangle1430106759}>
            <div className={styles.rectangle1430106765} />
            <div className={styles.rectangle1430106766} />
          </div>
          <div className={styles.rectangle1430106767} />
          <div className={styles.frame6}>
            <CarouselComponent
              images={greatWeatherCarouselImages}
              itemWidth={24.8125}
              itemGap={2.8125}
              speed={0.2}
              className={styles.carouselWrapper}
            />
          </div>
          <div className={styles.rectangle1430106762}>
            <div className={styles.autoWrapper5}>
              <div className={styles.frame2147229714}>
                <p className={styles.greatWeather}>Great Weather</p>
                <p className={styles.realTimeWeatherUpdat}>
                  Real-time Weather Updates: We provide you with the latest weather
                  data to ensure that your travel plans are not affected by unexpected
                  weather changes.
                  <br />
                  <br />
                  All-day Weather Overview: From sunrise to sunset, get a
                  comprehensive understanding of all-day weather changes to help you
                  make the best daily decisions.
                  <br />
                  <br />
                  Weekly Weather Forecast: Get a 7-day weather forecast in advance to
                  plan outdoor activities or arrange your work schedule in advance.
                  <br />
                  <br />
                  Lifestyle Index Guide: Access a variety of lifestyle indexes,
                  including humidity and UV index, to help you dress appropriately and
                  make smart decisions.
                </p>
                <img
                  src="/images/5782dcbf7a1beb8713bfa846d82745b3.png"
                  className={styles.frame2147229722}
                />
              </div>
              <p className={styles.a46}>4.6</p>
            </div>
            <div className={styles.autoWrapper6}>
              <div
                className={styles.buttonWeather}
                onClick={() => window.open('https://play.google.com/store/apps/details?id=com.great.forecast.weatherinfomation', '_blank')}
              >
                <div className={styles.frame261}>
                  <div className={styles.ellipse48} />
                  <p className={styles.getTheApp}>Get the App</p>
                  <img
                    src="/images/c6764a296886ba7216434cabdfc62ab0.png"
                    className={styles.arrowRightLongLine}
                  />
                </div>
              </div>
              <div className={styles.frame2147229712}>
                <div className={styles.ellipse7402} />
                <div className={styles.ellipse7403} />
                <div className={styles.ellipse7404} />
                <div className={styles.rectangle1430106781} />
              </div>
            </div>
          </div>
        </div>
        <div className={styles.frame2147229723}>
          <div className={styles.rectangle1430106763}>
            <div className={styles.rectangle14301067652} />
            <div className={styles.rectangle14301067662} />
            <div className={styles.rectangle14301067672} />
          </div>
          <div className={styles.frame8}>
            <CarouselComponent
              images={pdfQuickViewerCarouselImages}
              itemWidth={24.8125}
              itemGap={2.8125}
              speed={0.3}
              className={styles.carouselWrapper}
            />
          </div>
          <div className={styles.rectangle1430106764}>
            <div className={styles.frame2147229715}>
              <p className={styles.pDfQuickViewer}>PDF Quick Viewer</p>
              <p className={styles.realTimeWeatherUpdat}>
                Core functions:
                <br />
                Quick browsing: quickly open and view documents, and read various
                documents smoothly.
                <br />
                One-click search: enter keywords to quickly locate target files and
                content.
                <br />
                Image to PDF: support converting images to PDF files for easy
                reference and storage.
                <br />
                File management: files can be classified, renamed, batch processed,
                etc.
                <br />
                <br />
                Reasons for choosing PDF Quick Viewer:
                <br />
                Fully functional: meet daily PDF file processing needs.
                <br />
                Simple operation: no complicated learning required, easy to get
                started.
              </p>
            </div>
            <div className={styles.autoWrapper7}>
              <div
                className={styles.buttonPdf}
                onClick={() => window.open('https://play.google.com/store/apps/details?id=com.pdffile.quickviewer', '_blank')}
              >
                <div className={styles.frame2147229729}>
                  <div className={styles.ellipse482} />
                  <p className={styles.getTheApp2}>Get the App</p>
                  <img
                    src="/images/e2a9c19d84d65cb53548aeb01a3a11ff.png"
                    className={styles.arrowRightLongLine2}
                  />
                </div>
              </div>
              <img
                src="/images/79ea0f0243587bbe77e03b44e01d0a6d.png"
                className={styles.frame21472297122}
              />
            </div>
          </div>
        </div>
        <div id="faqs">
          <BigQuestions />
        </div>
        <div className={styles.rectangle1430106768} id="joinus">
          <p className={styles.letSMakeItHappenToge}>Let’s make it happen together</p>
          <p className={styles.bintangdisurga333Gma}><EMAIL></p>
        </div>
        <div className={styles.autoWrapper9}>
          <img
            src="/images/996c05148287684b2e8b23d4c9918a4c.png"
            className={styles.vector176}
          />
          <img
            src="/images/6ad2bbf4dc0594c4ed37f3141981d60e.png"
            className={styles.cursorWithAFace}
          />
          <img
            src="/images/6c7ed9f764e12575dbae47326045c65e.png"
            className={styles.smileyIcon}
          />
          <img
            src="/images/2c2a412ce8407c37db3f9a7790ab3e23.png"
            className={styles.group15}
          />
        </div>
      </div>
    </div>
  );
}

export default App;

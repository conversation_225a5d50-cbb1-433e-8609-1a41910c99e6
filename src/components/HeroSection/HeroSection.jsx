import React, { useState, useEffect } from 'react';
import styles from './HeroSection.module.scss';

/**
 * HeroSection Component
 *
 * A hero section that displays the main headline using animated text SVGs.
 * Based on Figma design: https://www.figma.com/design/5liAdEWw4jsBOXeMDtt73a/web-%E5%BC%80%E5%8F%91%E8%80%85%E7%BD%91%E7%AB%99---yy?node-id=1042-8153
 *
 * @returns {JSX.Element} The HeroSection component
 */
const HeroSection = () => {
  const [animationTriggered, setAnimationTriggered] = useState(false);

  useEffect(() => {
    const handleFirstInteraction = () => {
      if (!animationTriggered) {
        setAnimationTriggered(true);
        // 移除事件监听器，确保只触发一次
        window.removeEventListener('mousemove', handleFirstInteraction);
        window.removeEventListener('wheel', handleFirstInteraction);
        window.removeEventListener('scroll', handleFirstInteraction);
      }
    };

    // 添加事件监听器
    window.addEventListener('mousemove', handleFirstInteraction);
    window.addEventListener('wheel', handleFirstInteraction);
    window.addEventListener('scroll', handleFirstInteraction);

    return () => {
      // 清理事件监听器
      window.removeEventListener('mousemove', handleFirstInteraction);
      window.removeEventListener('wheel', handleFirstInteraction);
      window.removeEventListener('scroll', handleFirstInteraction);
    };
  }, [animationTriggered]);

  return (
    <div className={styles.heroSection}>
      {/* Background SVG */}
      <img
        src="/home-bg.svg"
        alt="Hero section background"
        className={styles.heroBg}
      />

      {/* Animated Text Elements */}
      <div className={styles.textContainer}>
        <img
          src="/Beyond.svg"
          alt="Beyond"
          className={`${styles.textElement} ${styles.beyond} ${animationTriggered ? styles.animated : ''}`}
        />
        <img
          src="/Optimization.svg"
          alt="Optimization"
          className={`${styles.textElement} ${styles.optimization} ${animationTriggered ? styles.animated : ''}`}
        />
        <img
          src="/WhereTech.svg"
          alt="Where Tech"
          className={`${styles.textElement} ${styles.whereTech} ${animationTriggered ? styles.animated : ''}`}
        />
        <img
          src="/MeetsIntuition.svg"
          alt="Meets Intuition"
          className={`${styles.textElement} ${styles.meetsIntuition} ${animationTriggered ? styles.animated : ''}`}
        />
      </div>

      {/* Line Elements */}
      <img
        src="/blue_line.svg"
        alt="Blue line decoration"
        className={`${styles.blueLine} ${animationTriggered ? styles.blueLineAnimated : ''}`}
      />
      <img
        src="/yellow_line.svg"
        alt="Yellow line decoration"
        className={`${styles.yellowLine} ${animationTriggered ? styles.yellowLineAnimated : ''}`}
      />

      {/* Other Pattern Elements */}
      <img
        src="/yellow_rectangle.svg"
        alt="Yellow rectangle decoration"
        className={`${styles.yellowRectangle} ${animationTriggered ? styles.yellowRectangleAnimated : ''}`}
      />
      <img
        src="/office_stuff_characters_with_different_emotions.svg"
        alt="Office characters decoration"
        className={`${styles.officeCharacters} ${animationTriggered ? styles.officeCharactersAnimated : ''}`}
      />
    </div>
  );
};

export default HeroSection;

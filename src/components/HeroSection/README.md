# HeroSection Component

A simple and efficient hero section component that displays the main headline using SVG directly from Figma.

## Features

- **SVG-based**: Uses the original SVG exported from Figma for pixel-perfect accuracy
- **Responsive**: Automatically scales on different screen sizes
- **Lightweight**: Minimal CSS and JavaScript overhead
- **Accessible**: Includes proper alt text for screen readers

## Design Source

Based on Figma design: https://www.figma.com/design/5liAdEWw4jsBOXeMDtt73a/web-%E5%BC%80%E5%8F%91%E8%80%85%E7%BD%91%E7%AB%99---yy?node-id=1042-8153

## Usage

```jsx
import HeroSection from './components/HeroSection';

function App() {
  return (
    <div>
      <HeroSection />
    </div>
  );
}
```

## Files

- `HeroSection.jsx` - React component
- `HeroSection.module.scss` - Styles for responsive layout
- `index.js` - Export file
- `/public/home.svg` - SVG asset from Figma

## Benefits of SVG Approach

1. **Perfect Fidelity**: Exact match to Figma design
2. **Scalability**: Vector graphics scale perfectly at any size
3. **Performance**: Single file load, no complex CSS layouts
4. **Maintainability**: Easy to update by replacing the SVG file
5. **Simplicity**: Minimal code complexity

## Responsive Behavior

- **Desktop**: Full-width SVG display
- **Mobile**: Responsive scaling with adjusted margins
- **All devices**: Maintains aspect ratio and visual integrity

## Browser Support

- All modern browsers with SVG support
- IE9+ (with SVG support)
- Mobile browsers

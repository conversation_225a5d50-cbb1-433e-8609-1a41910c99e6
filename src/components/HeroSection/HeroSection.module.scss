.heroSection {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  overflow: hidden;

  @media (max-width: 768px) {
    // 移动端保持相同的绝对定位
  }
}

.heroBg {
  width: 100%;
  max-width: 1920px;
  height: auto;
  display: block;
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1;

  @media (max-width: 768px) {
    width: 100%;
    height: auto;
  }
}

.textContainer {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  max-width: 1920px;
  height: 100%;
  z-index: 2;
}

.textElement {
  position: absolute;
  height: auto;
  z-index: 3;
  // 🎯 调整这个值来缩放所有文字 SVG 的大小
  // 1.0 = 原始大小，0.8 = 80% 大小，1.2 = 120% 大小
  --text-scale: 0.9;

  // 初始状态：隐藏且稍微向下偏移
  opacity: 0;
  transform: translateY(50px) scale(var(--text-scale));
  transform-origin: left top;

  // 动画过渡效果：500ms 平滑过渡
  transition: opacity 500ms ease-out, transform 500ms ease-out;

  // 动画触发后的状态
  &.animated {
    opacity: 1;
    transform: translateY(0) scale(var(--text-scale));
  }

  @media (max-width: 768px) {
    // 平板端：基础缩放 × 0.8
    transform: translateY(50px) scale(calc(var(--text-scale) * 0.8));

    &.animated {
      transform: translateY(0) scale(calc(var(--text-scale) * 0.8));
    }
  }

  @media (max-width: 480px) {
    // 手机端：基础缩放 × 0.6
    transform: translateY(50px) scale(calc(var(--text-scale) * 0.6));

    &.animated {
      transform: translateY(0) scale(calc(var(--text-scale) * 0.6));
    }
  }
}

// Text positioning based on 1920x1244 canvas, converted to rem
.beyond {
  --top-px: 216;
  --left-px: 163;
  top: calc(var(--top-px) / 16 * 1rem);
  left: calc(var(--left-px) / 16 * 1rem);
  width: auto;
}

.optimization {
  --top-px: 469;
  --left-px: 163;
  top: calc(var(--top-px) / 16 * 1rem);
  left: calc(var(--left-px) / 16 * 1rem);
  width: auto;
}

.whereTech {
  --top-px: 722;
  --left-px: 163;
  top: calc(var(--top-px) / 16 * 1rem);
  left: calc(var(--left-px) / 16 * 1rem);
  width: auto;
}

.meetsIntuition {
  --top-px: 975;
  --left-px: 163;
  top: calc(var(--top-px) / 16 * 1rem);
  left: calc(var(--left-px) / 16 * 1rem);
  width: auto;
}

// Line positioning based on 1920x1244 canvas
.blueLine {
  position: absolute;
  height: auto;
  z-index: 2;
  width: auto;
  // 🎯 调整这个值来缩放蓝线的大小
  --line-scale: 0.9;
  --top-px: 940;
  --left-px: 55;
  top: calc(var(--top-px) / 16 * 1rem);
  left: calc(var(--left-px) / 16 * 1rem);
  transform: scale(var(--line-scale));
  transform-origin: left top;

  // 初始状态：隐藏
  opacity: 0;
  // 动画过渡效果：500ms 透明度渐变
  transition: opacity 500ms ease-out;

  // 动画触发后的状态
  &.blueLineAnimated {
    opacity: 1;
  }
}

.yellowLine {
  position: absolute;
  height: auto;
  z-index: 2;
  width: auto;
  // 🎯 调整这个值来缩放黄线的大小
  --line-scale: 0.9;
  --top-px: 610;
  --left-px: 722;
  top: calc(var(--top-px) / 16 * 1rem);
  left: calc(var(--left-px) / 16 * 1rem);
  transform: scale(var(--line-scale));
  transform-origin: left top;

  // 初始状态：隐藏
  opacity: 0;
  // 黄线立即出现，无过渡效果
  transition: none;

  // 动画触发后的状态：立即显示
  &.yellowLineAnimated {
    opacity: 1;
  }
}

// Other pattern elements positioning
.yellowRectangle {
  position: absolute;
  height: auto;
  z-index: 1; // 背景层级，低于其他元素
  width: auto;
  // 🎯 调整这个值来缩放黄色矩形的大小
  --pattern-scale: 0.9;
  --top-px: 0; // 您已调整为 0
  --left-px: 141;
  top: calc(var(--top-px) / 16 * 1rem);
  left: calc(var(--left-px) / 16 * 1rem);

  // 初始状态：隐藏且在屏幕下方
  opacity: 0;
  transform: translateY(100vh) scale(var(--pattern-scale));
  transform-origin: left top;

  // 动画过渡效果
  transition: opacity 500ms ease-out, transform 500ms ease-out;

  // 动画触发后的状态
  &.yellowRectangleAnimated {
    opacity: 1;
    transform: translateY(0) scale(var(--pattern-scale));
  }
}

.officeCharacters {
  position: absolute;
  height: auto;
  z-index: 2;
  width: auto;
  // 🎯 调整这个值来缩放办公角色的大小
  --pattern-scale: 0.9;
  --top-px: 267;
  --left-px: 1420;
  top: calc(var(--top-px) / 16 * 1rem);
  left: calc(var(--left-px) / 16 * 1rem);

  // 初始状态：隐藏且在屏幕右方
  opacity: 0;
  transform: translateX(100vw) scale(var(--pattern-scale));
  transform-origin: left top;

  // 动画过渡效果
  transition: opacity 500ms ease-out, transform 500ms ease-out;

  // 动画触发后的状态
  &.officeCharactersAnimated {
    opacity: 1;
    transform: translateX(0) scale(var(--pattern-scale));
  }
}

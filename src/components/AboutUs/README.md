# AboutUs Component

A responsive About Us section component that displays company information using SVG directly from Figma for perfect design fidelity and consistent typography.

## Features

- **SVG-based**: Uses the original SVG exported from Figma for pixel-perfect accuracy
- **Typography Consistency**: Solves font inconsistency issues from the original template
- **Responsive**: Automatically scales on different screen sizes
- **Lightweight**: Minimal CSS and JavaScript overhead
- **Accessible**: Includes proper alt text for screen readers

## Problem Solved

This component replaces the original About Us section in the template that had font inconsistency issues. By using the SVG directly from Figma, we ensure:
- Exact font rendering as designed
- Consistent visual appearance
- Perfect alignment and spacing

## Usage

```jsx
import AboutUs from './components/AboutUs';

function App() {
  return (
    <div>
      <AboutUs />
    </div>
  );
}
```

## Files

- `AboutUs.jsx` - React component
- `AboutUs.module.scss` - Styles for responsive layout
- `index.js` - Export file
- `/public/about_us.svg` - SVG asset from Figma

## Integration

This component seamlessly replaces the original About Us section while maintaining the page flow and structure. It's positioned between the HeroSection and the "we can be your TOP Choice" section.

## Browser Support

- All modern browsers with SVG support
- IE9+ (with SVG support)
- Mobile browsers
